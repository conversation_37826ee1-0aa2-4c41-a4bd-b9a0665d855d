-- Add Stripe customer ID to profiles table
-- Run this migration to add Stripe integration to existing user profiles

-- Add stripe_customer_id column to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT UNIQUE;

-- Add index for Stripe customer ID lookups
CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer_id ON profiles(stripe_customer_id);

-- Add constraint to ensure stripe_customer_id format (starts with 'cus_')
ALTER TABLE profiles 
ADD CONSTRAINT check_stripe_customer_id_format 
CHECK (stripe_customer_id IS NULL OR stripe_customer_id LIKE 'cus_%');

-- Update existing users to have null stripe_customer_id (will be populated when they subscribe)
-- No need to update existing records as the column defaults to NULL
