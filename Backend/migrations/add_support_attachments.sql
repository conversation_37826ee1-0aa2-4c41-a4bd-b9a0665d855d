-- Add attachments support to support_requests table
-- This migration adds a JSONB column to store attachment metadata

ALTER TABLE support_requests 
ADD COLUMN attachments JSONB DEFAULT '[]'::jsonb;

-- Add a comment to document the expected structure
COMMENT ON COLUMN support_requests.attachments IS 'Array of attachment objects with fields: {filename: string, original_name: string, mime_type: string, size: number, url: string}';

-- Add an index for querying attachments
CREATE INDEX idx_support_requests_attachments ON support_requests USING gin (attachments);