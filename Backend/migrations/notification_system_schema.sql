-- =====================================================
-- NOTIFICATION SYSTEM DATABASE SCHEMA
-- Comprehensive notification system with custom scheduling and real-time notifications
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. NOTIFICATION TYPES TABLE
-- =====================================================
CREATE TABLE notification_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    display_name TEXT NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    default_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert default notification types
INSERT INTO notification_types (name, display_name, description, is_system, default_enabled) VALUES
('new_message', 'New Messages', 'Notifications for new chat messages', TRUE, TRUE),
('activity_status_change', 'Activity Updates', 'Notifications when activity status changes', TRUE, TRUE),
('support_request_update', 'Support Updates', 'Notifications for support request status changes', TRUE, TRUE),
('custom_reminder', 'Custom Reminders', 'Custom scheduled notifications', FALSE, TRUE),
('system_announcement', 'System Announcements', 'Important system announcements', TRUE, TRUE),
('assignment_notification', 'Assignment Alerts', 'Notifications when assigned to tasks', TRUE, TRUE);

-- =====================================================
-- 2. USER DEVICES TABLE (for push notification tokens)
-- =====================================================
CREATE TABLE user_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_token TEXT NOT NULL,
    device_type TEXT NOT NULL CHECK (device_type IN ('ios', 'android', 'web')),
    device_name TEXT,
    app_version TEXT,
    os_version TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, device_token)
);

-- =====================================================
-- 3. NOTIFICATION PREFERENCES TABLE
-- =====================================================
CREATE TABLE notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type_id UUID NOT NULL REFERENCES notification_types(id) ON DELETE CASCADE,
    push_enabled BOOLEAN DEFAULT TRUE,
    email_enabled BOOLEAN DEFAULT FALSE,
    sms_enabled BOOLEAN DEFAULT FALSE,
    in_app_enabled BOOLEAN DEFAULT TRUE,
    quiet_hours_start TIME,
    quiet_hours_end TIME,
    timezone TEXT DEFAULT 'UTC',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, notification_type_id)
);

-- =====================================================
-- 4. CUSTOM NOTIFICATIONS TABLE (for scheduled notifications)
-- =====================================================
CREATE TABLE custom_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- NULL for all users
    target_role TEXT CHECK (target_role IN ('user', 'support', 'admin')), -- NULL for specific user
    title TEXT NOT NULL CHECK (length(title) >= 1 AND length(title) <= 100),
    message TEXT NOT NULL CHECK (length(message) >= 1 AND length(message) <= 500),
    notification_type_id UUID NOT NULL REFERENCES notification_types(id),
    scheduled_for TIMESTAMPTZ NOT NULL,
    sent_at TIMESTAMPTZ,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSONB, -- For recurring notifications (daily, weekly, monthly)
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'sent', 'failed', 'cancelled')),
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 5. NOTIFICATION HISTORY TABLE (all sent notifications)
-- =====================================================
CREATE TABLE notification_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type_id UUID NOT NULL REFERENCES notification_types(id),
    custom_notification_id UUID REFERENCES custom_notifications(id) ON DELETE SET NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    delivery_method TEXT NOT NULL CHECK (delivery_method IN ('push', 'email', 'sms', 'in_app')),
    sent_at TIMESTAMPTZ DEFAULT NOW(),
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    clicked_at TIMESTAMPTZ,
    status TEXT DEFAULT 'sent' CHECK (status IN ('sent', 'delivered', 'failed', 'read', 'clicked')),
    error_message TEXT,
    device_token TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 6. NOTIFICATION QUEUE TABLE (for processing)
-- =====================================================
CREATE TABLE notification_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    notification_type_id UUID NOT NULL REFERENCES notification_types(id),
    custom_notification_id UUID REFERENCES custom_notifications(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    delivery_method TEXT NOT NULL CHECK (delivery_method IN ('push', 'email', 'sms', 'in_app')),
    scheduled_for TIMESTAMPTZ DEFAULT NOW(),
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'cancelled')),
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 7. CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- User devices indexes
CREATE INDEX idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX idx_user_devices_token ON user_devices(device_token);
CREATE INDEX idx_user_devices_active ON user_devices(is_active, last_used_at DESC);

-- Notification preferences indexes
CREATE INDEX idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX idx_notification_preferences_type ON notification_preferences(notification_type_id);

-- Custom notifications indexes
CREATE INDEX idx_custom_notifications_scheduled ON custom_notifications(scheduled_for, status);
CREATE INDEX idx_custom_notifications_creator ON custom_notifications(created_by);
CREATE INDEX idx_custom_notifications_target_user ON custom_notifications(target_user_id);
CREATE INDEX idx_custom_notifications_target_role ON custom_notifications(target_role);
CREATE INDEX idx_custom_notifications_recurring ON custom_notifications(is_recurring, status);

-- Notification history indexes
CREATE INDEX idx_notification_history_user_id ON notification_history(user_id, sent_at DESC);
CREATE INDEX idx_notification_history_type ON notification_history(notification_type_id);
CREATE INDEX idx_notification_history_status ON notification_history(status, sent_at DESC);
CREATE INDEX idx_notification_history_unread ON notification_history(user_id, read_at) WHERE read_at IS NULL;

-- Notification queue indexes
CREATE INDEX idx_notification_queue_scheduled ON notification_queue(scheduled_for, status);
CREATE INDEX idx_notification_queue_user_id ON notification_queue(user_id);
CREATE INDEX idx_notification_queue_processing ON notification_queue(status, attempts);

-- =====================================================
-- 8. CREATE FUNCTIONS FOR NOTIFICATION MANAGEMENT
-- =====================================================

-- Function to create default notification preferences for new users
CREATE OR REPLACE FUNCTION create_default_notification_preferences(user_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO notification_preferences (user_id, notification_type_id, push_enabled, email_enabled, in_app_enabled)
    SELECT 
        user_id,
        nt.id,
        nt.default_enabled,
        FALSE, -- Email disabled by default
        nt.default_enabled
    FROM notification_types nt
    WHERE nt.is_active = TRUE
    ON CONFLICT (user_id, notification_type_id) DO NOTHING;
END;
$$;

-- Function to queue a notification
CREATE OR REPLACE FUNCTION queue_notification(
    p_user_id UUID,
    p_notification_type TEXT,
    p_title TEXT,
    p_message TEXT,
    p_delivery_method TEXT DEFAULT 'push',
    p_scheduled_for TIMESTAMPTZ DEFAULT NOW(),
    p_custom_notification_id UUID DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    notification_type_id UUID;
    queue_id UUID;
BEGIN
    -- Get notification type ID
    SELECT id INTO notification_type_id
    FROM notification_types
    WHERE name = p_notification_type AND is_active = TRUE;
    
    IF notification_type_id IS NULL THEN
        RAISE EXCEPTION 'Invalid notification type: %', p_notification_type;
    END IF;
    
    -- Insert into queue
    INSERT INTO notification_queue (
        user_id,
        notification_type_id,
        custom_notification_id,
        title,
        message,
        delivery_method,
        scheduled_for,
        metadata
    ) VALUES (
        p_user_id,
        notification_type_id,
        p_custom_notification_id,
        p_title,
        p_message,
        p_delivery_method,
        p_scheduled_for,
        p_metadata
    ) RETURNING id INTO queue_id;
    
    RETURN queue_id;
END;
$$;

-- Function to check if user wants to receive notification
CREATE OR REPLACE FUNCTION should_send_notification(
    p_user_id UUID,
    p_notification_type TEXT,
    p_delivery_method TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    pref_enabled BOOLEAN := FALSE;
    type_id UUID;
    current_time TIME;
    quiet_start TIME;
    quiet_end TIME;
    user_timezone TEXT;
BEGIN
    -- Get notification type ID
    SELECT id INTO type_id
    FROM notification_types
    WHERE name = p_notification_type AND is_active = TRUE;
    
    IF type_id IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Get user preferences
    SELECT 
        CASE 
            WHEN p_delivery_method = 'push' THEN np.push_enabled
            WHEN p_delivery_method = 'email' THEN np.email_enabled
            WHEN p_delivery_method = 'sms' THEN np.sms_enabled
            WHEN p_delivery_method = 'in_app' THEN np.in_app_enabled
            ELSE FALSE
        END,
        np.quiet_hours_start,
        np.quiet_hours_end,
        np.timezone
    INTO pref_enabled, quiet_start, quiet_end, user_timezone
    FROM notification_preferences np
    WHERE np.user_id = p_user_id AND np.notification_type_id = type_id;
    
    -- If no preferences found, use default (enabled)
    IF NOT FOUND THEN
        pref_enabled := TRUE;
    END IF;
    
    -- Check if notification is enabled
    IF NOT pref_enabled THEN
        RETURN FALSE;
    END IF;
    
    -- Check quiet hours
    IF quiet_start IS NOT NULL AND quiet_end IS NOT NULL THEN
        current_time := (NOW() AT TIME ZONE COALESCE(user_timezone, 'UTC'))::TIME;
        
        -- Handle quiet hours that span midnight
        IF quiet_start <= quiet_end THEN
            IF current_time BETWEEN quiet_start AND quiet_end THEN
                RETURN FALSE;
            END IF;
        ELSE
            IF current_time >= quiet_start OR current_time <= quiet_end THEN
                RETURN FALSE;
            END IF;
        END IF;
    END IF;
    
    RETURN TRUE;
END;
$$;

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(p_notification_id UUID, p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE notification_history
    SET read_at = NOW()
    WHERE id = p_notification_id 
    AND user_id = p_user_id 
    AND read_at IS NULL;
    
    RETURN FOUND;
END;
$$;

-- =====================================================
-- 9. CREATE TRIGGERS FOR AUTOMATIC NOTIFICATIONS
-- =====================================================

-- Trigger function for new message notifications
CREATE OR REPLACE FUNCTION trigger_new_message_notification()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    conversation_user_id UUID;
    message_sender_name TEXT;
BEGIN
    -- Get the conversation owner (recipient of notification)
    SELECT c.user_id INTO conversation_user_id
    FROM conversations c
    WHERE c.id = NEW.conversation_id;
    
    -- Don't notify if sender is the same as conversation owner
    IF NEW.sender_id = conversation_user_id THEN
        RETURN NEW;
    END IF;
    
    -- Get sender name for notification
    SELECT p.name INTO message_sender_name
    FROM profiles p
    WHERE p.id = NEW.sender_id;
    
    -- Queue notification
    PERFORM queue_notification(
        conversation_user_id,
        'new_message',
        'New Message from ' || COALESCE(message_sender_name, 'Unknown'),
        LEFT(NEW.content, 100) || CASE WHEN LENGTH(NEW.content) > 100 THEN '...' ELSE '' END,
        'push',
        NOW(),
        NULL,
        jsonb_build_object('conversation_id', NEW.conversation_id, 'message_id', NEW.id)
    );
    
    RETURN NEW;
END;
$$;

-- Create trigger for new messages
DROP TRIGGER IF EXISTS trigger_new_message_notification ON messages;
CREATE TRIGGER trigger_new_message_notification
    AFTER INSERT ON messages
    FOR EACH ROW
    WHEN (NEW.message_type != 'system')
    EXECUTE FUNCTION trigger_new_message_notification();

-- Trigger function for support request status changes
CREATE OR REPLACE FUNCTION trigger_support_request_notification()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Only notify on status changes
    IF TG_OP = 'UPDATE' AND OLD.status = NEW.status THEN
        RETURN NEW;
    END IF;
    
    -- Queue notification for status change
    PERFORM queue_notification(
        NEW.user_id,
        'support_request_update',
        'Support Request Updated',
        'Your support request "' || NEW.title || '" status changed to ' || NEW.status,
        'push',
        NOW(),
        NULL,
        jsonb_build_object('support_request_id', NEW.id, 'new_status', NEW.status)
    );
    
    RETURN NEW;
END;
$$;

-- =====================================================
-- 10. CREATE TRIGGERS FOR UPDATED_AT COLUMNS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER trigger_user_devices_updated_at
    BEFORE UPDATE ON user_devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_notification_preferences_updated_at
    BEFORE UPDATE ON notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_custom_notifications_updated_at
    BEFORE UPDATE ON custom_notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_notification_queue_updated_at
    BEFORE UPDATE ON notification_queue
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 11. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE notification_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_queue ENABLE ROW LEVEL SECURITY;

-- Notification types policies (read-only for all authenticated users)
CREATE POLICY "Users can view notification types" ON notification_types
    FOR SELECT TO authenticated
    USING (is_active = TRUE);

-- User devices policies
CREATE POLICY "Users can manage their own devices" ON user_devices
    FOR ALL TO authenticated
    USING (auth.uid() = user_id);

-- Notification preferences policies
CREATE POLICY "Users can manage their own preferences" ON notification_preferences
    FOR ALL TO authenticated
    USING (auth.uid() = user_id);

-- Custom notifications policies
CREATE POLICY "Users can view their own custom notifications" ON custom_notifications
    FOR SELECT TO authenticated
    USING (auth.uid() = created_by OR auth.uid() = target_user_id);

CREATE POLICY "Support users can create custom notifications" ON custom_notifications
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles p 
            WHERE p.id = auth.uid() 
            AND p.role IN ('support', 'admin')
        )
    );

CREATE POLICY "Users can update their own custom notifications" ON custom_notifications
    FOR UPDATE TO authenticated
    USING (auth.uid() = created_by);

-- Notification history policies
CREATE POLICY "Users can view their own notification history" ON notification_history
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notification history" ON notification_history
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id);

-- Notification queue policies (service role only for processing)
CREATE POLICY "Service role can manage notification queue" ON notification_queue
    FOR ALL TO service_role
    USING (TRUE);

-- =====================================================
-- 12. GRANT PERMISSIONS
-- =====================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated, service_role;

-- Grant permissions on tables
GRANT SELECT ON notification_types TO authenticated;
GRANT ALL ON user_devices TO authenticated;
GRANT ALL ON notification_preferences TO authenticated;
GRANT ALL ON custom_notifications TO authenticated;
GRANT SELECT, UPDATE ON notification_history TO authenticated;
GRANT ALL ON notification_queue TO service_role;

-- Grant permissions on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated, service_role;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION create_default_notification_preferences(UUID) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION queue_notification(UUID, TEXT, TEXT, TEXT, TEXT, TIMESTAMPTZ, UUID, JSONB) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION should_send_notification(UUID, TEXT, TEXT) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION mark_notification_read(UUID, UUID) TO authenticated;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT
    'NOTIFICATION SYSTEM DATABASE SCHEMA CREATED SUCCESSFULLY!' as status,
    'All tables, indexes, functions, triggers, and RLS policies are ready.' as message,
    'You can now implement the notification service and mobile app integration.' as next_step;