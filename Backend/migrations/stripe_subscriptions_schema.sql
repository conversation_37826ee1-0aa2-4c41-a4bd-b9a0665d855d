-- Stripe Subscriptions and Credits Management Schema
-- Run this migration to add subscription and credits support

-- =====================================================
-- 1. SUBSCRIPTION PLANS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    stripe_product_id TEXT NOT NULL UNIQUE,
    stripe_price_id TEXT NOT NULL UNIQUE,
    plan_name TEXT NOT NULL CHECK (plan_name IN ('starting', 'scaling', 'summit')),
    display_name TEXT NOT NULL,
    description TEXT,
    price_amount INTEGER NOT NULL, -- Price in cents
    currency TEXT NOT NULL DEFAULT 'usd',
    billing_interval TEXT NOT NULL CHECK (billing_interval IN ('month', 'year')),
    credits_included INTEGER NOT NULL,
    features JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 2. USER SUBSCRIPTIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_customer_id TEXT NOT NULL,
    stripe_subscription_id TEXT NOT NULL UNIQUE,
    plan_id UUID NOT NULL REFERENCES subscription_plans(id),
    status TEXT NOT NULL CHECK (status IN ('active', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid')),
    current_period_start TIMESTAMPTZ NOT NULL,
    current_period_end TIMESTAMPTZ NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    canceled_at TIMESTAMPTZ,
    trial_start TIMESTAMPTZ,
    trial_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 3. USER CREDITS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS user_credits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    current_credits INTEGER NOT NULL DEFAULT 0,
    total_credits_allocated INTEGER NOT NULL DEFAULT 0,
    credits_used INTEGER NOT NULL DEFAULT 0,
    last_reset_date TIMESTAMPTZ DEFAULT NOW(),
    next_reset_date TIMESTAMPTZ,
    subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 4. CREDITS USAGE HISTORY TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS credits_usage_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    credits_used INTEGER NOT NULL,
    action_type TEXT NOT NULL, -- 'chat_message', 'voice_transcription', 'ai_response', etc.
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 5. STRIPE WEBHOOK EVENTS TABLE (for idempotency)
-- =====================================================
CREATE TABLE IF NOT EXISTS stripe_webhook_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    stripe_event_id TEXT NOT NULL UNIQUE,
    event_type TEXT NOT NULL,
    processed_at TIMESTAMPTZ DEFAULT NOW(),
    data JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Subscription plans indexes
CREATE INDEX IF NOT EXISTS idx_subscription_plans_stripe_product ON subscription_plans(stripe_product_id);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_stripe_price ON subscription_plans(stripe_price_id);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_active ON subscription_plans(is_active, plan_name);

-- User subscriptions indexes
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_customer ON user_subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_subscription ON user_subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_period_end ON user_subscriptions(current_period_end);

-- User credits indexes
CREATE INDEX IF NOT EXISTS idx_user_credits_user_id ON user_credits(user_id);
CREATE INDEX IF NOT EXISTS idx_user_credits_next_reset ON user_credits(next_reset_date);

-- Credits usage history indexes
CREATE INDEX IF NOT EXISTS idx_credits_usage_user_id ON credits_usage_history(user_id);
CREATE INDEX IF NOT EXISTS idx_credits_usage_created_at ON credits_usage_history(created_at);
CREATE INDEX IF NOT EXISTS idx_credits_usage_action_type ON credits_usage_history(action_type);

-- Stripe webhook events indexes
CREATE INDEX IF NOT EXISTS idx_stripe_webhook_events_stripe_id ON stripe_webhook_events(stripe_event_id);
CREATE INDEX IF NOT EXISTS idx_stripe_webhook_events_type ON stripe_webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_stripe_webhook_events_created_at ON stripe_webhook_events(created_at);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Update updated_at timestamp for subscription_plans
CREATE OR REPLACE FUNCTION update_subscription_plans_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_subscription_plans_updated_at
    BEFORE UPDATE ON subscription_plans
    FOR EACH ROW
    EXECUTE FUNCTION update_subscription_plans_updated_at();

-- Update updated_at timestamp for user_subscriptions
CREATE OR REPLACE FUNCTION update_user_subscriptions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_subscriptions_updated_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_subscriptions_updated_at();

-- Update updated_at timestamp for user_credits
CREATE OR REPLACE FUNCTION update_user_credits_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_user_credits_updated_at
    BEFORE UPDATE ON user_credits
    FOR EACH ROW
    EXECUTE FUNCTION update_user_credits_updated_at();

-- =====================================================
-- INITIAL DATA - SUBSCRIPTION PLANS
-- =====================================================

-- Insert the three subscription plans
-- Note: Stripe product and price IDs will be updated when created in Stripe
INSERT INTO subscription_plans (
    stripe_product_id,
    stripe_price_id,
    plan_name,
    display_name,
    description,
    price_amount,
    currency,
    billing_interval,
    credits_included,
    features
) VALUES
(
    'prod_starting_placeholder',
    'price_starting_placeholder',
    'starting',
    'Starting Plan',
    'Perfect for getting started with AI assistance',
    999, -- $9.99
    'usd',
    'month',
    80,
    '{"ai_chat_support": "basic", "email_support": true, "response_time": "standard", "voice_messages": false, "priority_support": false, "custom_integrations": false}'::jsonb
),
(
    'prod_scaling_placeholder',
    'price_scaling_placeholder',
    'scaling',
    'Scaling Plan',
    'Advanced features for growing businesses',
    1999, -- $19.99
    'usd',
    'month',
    160,
    '{"ai_chat_support": "advanced", "email_support": "priority", "response_time": "faster", "voice_messages": true, "priority_support": false, "custom_integrations": false}'::jsonb
),
(
    'prod_summit_placeholder',
    'price_summit_placeholder',
    'summit',
    'Summit Plan',
    'Premium experience with all features included',
    3999, -- $39.99
    'usd',
    'month',
    400,
    '{"ai_chat_support": "premium", "email_support": "24/7_priority", "response_time": "instant", "voice_messages": true, "priority_support": true, "custom_integrations": true}'::jsonb
)
ON CONFLICT (stripe_product_id) DO NOTHING;

-- =====================================================
-- CLEANUP FUNCTIONS
-- =====================================================

-- Function to clean up old webhook events (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_webhook_events()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM stripe_webhook_events 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old credits usage history (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_credits_history()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM credits_usage_history 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SCHEMA COMPLETE
-- =====================================================
-- This schema provides:
-- 1. Complete subscription plan management
-- 2. User subscription tracking with Stripe integration
-- 3. Credits allocation and usage tracking
-- 4. Webhook event processing and idempotency
-- 5. Performance optimized with proper indexes
-- 6. Automatic timestamp updates
-- 7. Data cleanup functions
-- =====================================================
