{"name": "ava-chatbot-backend", "version": "1.0.0", "description": "<PERSON>end with Jira Integration and Human Support", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "test": "jest", "test:stripe": "jest src/tests/stripe.test.ts", "test:integration": "node scripts/test-stripe-integration.js", "setup-stripe": "node scripts/setup-stripe-products.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["chatbot", "jira", "support", "supabase", "firebase", "notifications"], "author": "Ava Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.4", "@types/node-cron": "^3.0.11", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@xenova/transformers": "^2.17.1", "axios": "^1.6.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.1.0", "express-slow-down": "^3.0.0", "express-validator": "^7.2.1", "ffmpeg-static": "^5.2.0", "firebase-admin": "^11.11.1", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.4", "helmet": "^7.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "node-cron": "^4.2.1", "openai": "^4.20.1", "qrcode": "^1.5.4", "speakeasy": "^2.0.0", "stripe": "^18.5.0", "uuid": "^9.0.1", "wavefile": "^11.0.0", "xss": "^1.0.15", "zod": "^3.22.4"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-rate-limit": "^5.1.3", "@types/fluent-ffmpeg": "^2.1.27", "@types/glob": "^8.1.0", "@types/hpp": "^0.2.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^2.0.0", "@types/node": "^20.10.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}