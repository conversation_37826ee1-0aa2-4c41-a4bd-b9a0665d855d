import express from 'express'
import { StripeService } from '@/services/stripe'
import WebhookSecurity from '@/utils/webhook-security'

const router = express.Router()

/**
 * This endpoint uses express.raw middleware to get the raw body
 * for webhook signature verification
 */
router.post(
  '/stripe',
  express.raw({ type: 'application/json' }),
  ...WebhookSecurity.stripeWebhookSecurity(),
  async (req, res) => {
    try {
      const signature = req.get('stripe-signature')
      
      if (!signature) {
        console.error('Missing Stripe signature header')
        return res.status(400).json({
          success: false,
          error: 'Missing Stripe signature',
        })
      }

      // Verify webhook signature and construct event
      let event
      try {
        // req.body is already a Buffer from express.raw middleware
        // Don't call toString() as it might alter the raw bytes needed for signature verification
        event = StripeService.verifyWebhookSignature(
          req.body,
          signature
        )
      } catch (error) {
        console.error('Webhook signature verification failed:', error)

        // For testing: If signature verification fails, try to parse the event anyway
        // REMOVE THIS IN PRODUCTION!
        if (process.env.NODE_ENV === 'development') {
          console.log('⚠️  Development mode: Processing webhook without signature verification')
          try {
            event = JSON.parse(req.body.toString())
          } catch (parseError) {
            console.error('Failed to parse webhook body:', parseError)
            return res.status(400).json({
              success: false,
              error: 'Invalid webhook signature and unable to parse body',
            })
          }
        } else {
          return res.status(400).json({
            success: false,
            error: 'Invalid webhook signature',
          })
        }
      }

      console.log(`Received Stripe webhook: ${event.type} (${event.id})`)

      // Process the webhook event
      await StripeService.processWebhookEvent(event)

      // Return success response to Stripe
      res.json({
        success: true,
        received: true,
        eventId: event.id,
        eventType: event.type,
      })
    } catch (error) {
      console.error('Webhook processing error:', error)
      
      // Return error response to Stripe
      res.status(400).json({
        success: false,
        error: error instanceof Error ? error.message : 'Webhook processing failed',
      })
    }
  }
)

export default router
