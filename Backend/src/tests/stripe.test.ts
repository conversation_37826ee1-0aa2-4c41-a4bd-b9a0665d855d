import { StripeService } from '@/services/stripe'
import CreditsManager from '@/services/credits'
import { supabaseAdmin } from '@/lib'

// Mock Stripe
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    customers: {
      create: jest.fn(),
      retrieve: jest.fn(),
    },
    subscriptions: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
      cancel: jest.fn(),
    },
    products: {
      create: jest.fn(),
      retrieve: jest.fn(),
    },
    prices: {
      create: jest.fn(),
      retrieve: jest.fn(),
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
  }))
})

// Mock Supabase
jest.mock('@/lib', () => ({
  supabaseAdmin: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
          order: jest.fn(() => ({
            range: jest.fn(),
          })),
        })),
        lte: jest.fn(() => ({
          eq: jest.fn(),
        })),
      })),
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn(),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(),
      })),
    })),
  },
}))

describe('StripeService', () => {
  const mockUserId = 'test-user-id'
  const mockEmail = '<EMAIL>'
  const mockCustomerId = 'cus_test123'
  const mockPriceId = 'price_test123'

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('createOrGetCustomer', () => {
    it('should return existing customer ID if user already has one', async () => {
      const mockProfile = { stripe_customer_id: mockCustomerId }
      
      ;(supabaseAdmin.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockProfile, error: null }),
          }),
        }),
      })

      const result = await StripeService.createOrGetCustomer(mockUserId, mockEmail)
      expect(result).toBe(mockCustomerId)
    })

    it('should create new customer if user does not have one', async () => {
      const mockProfile = { stripe_customer_id: null }
      const mockCustomer = { id: mockCustomerId }

      ;(supabaseAdmin.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockProfile, error: null }),
          }),
        }),
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({ error: null }),
        }),
      })

      // Mock Stripe customer creation
      const mockStripe = require('stripe')()
      mockStripe.customers.create.mockResolvedValue(mockCustomer)

      const result = await StripeService.createOrGetCustomer(mockUserId, mockEmail, 'Test User')
      
      expect(mockStripe.customers.create).toHaveBeenCalledWith({
        email: mockEmail,
        name: 'Test User',
        metadata: { user_id: mockUserId },
      })
      expect(result).toBe(mockCustomerId)
    })
  })

  describe('getSubscriptionPlans', () => {
    it('should return active subscription plans', async () => {
      const mockPlans = [
        { id: '1', plan_name: 'starting', price_amount: 999 },
        { id: '2', plan_name: 'scaling', price_amount: 1999 },
      ]

      ;(supabaseAdmin.from as jest.Mock).mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({ data: mockPlans, error: null }),
          }),
        }),
      })

      const result = await StripeService.getSubscriptionPlans()
      expect(result).toEqual(mockPlans)
    })
  })

  describe('createSubscription', () => {
    it('should create subscription and store in database', async () => {
      const mockPlan = {
        id: 'plan-id',
        stripe_price_id: mockPriceId,
        plan_name: 'starting',
        credits_included: 80,
      }

      const mockSubscription = {
        id: 'sub_test123',
        status: 'active',
        current_period_start: 1640995200,
        current_period_end: 1643673600,
        cancel_at_period_end: false,
        latest_invoice: {
          payment_intent: {
            client_secret: 'pi_test_client_secret',
          },
        },
      }

      const mockUserSubscription = {
        id: 'user-sub-id',
        user_id: mockUserId,
        stripe_subscription_id: mockSubscription.id,
      }

      // Mock plan lookup
      ;(supabaseAdmin.from as jest.Mock)
        .mockReturnValueOnce({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({ data: mockPlan, error: null }),
            }),
          }),
        })
        // Mock subscription insert
        .mockReturnValueOnce({
          insert: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({ data: mockUserSubscription, error: null }),
            }),
          }),
        })

      // Mock Stripe subscription creation
      const mockStripe = require('stripe')()
      mockStripe.subscriptions.create.mockResolvedValue(mockSubscription)

      const result = await StripeService.createSubscription(mockUserId, mockCustomerId, mockPriceId)

      expect(mockStripe.subscriptions.create).toHaveBeenCalledWith({
        customer: mockCustomerId,
        items: [{ price: mockPriceId }],
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        metadata: {
          user_id: mockUserId,
          plan_name: mockPlan.plan_name,
        },
      })

      expect(result.subscription).toEqual(mockSubscription)
      expect(result.userSubscription).toEqual(mockUserSubscription)
    })
  })

  describe('webhook processing', () => {
    it('should process payment succeeded webhook', async () => {
      const mockEvent = {
        id: 'evt_test123',
        type: 'invoice.payment_succeeded',
        data: {
          object: {
            subscription: 'sub_test123',
          },
        },
      }

      const mockSubscription = {
        id: 'sub_test123',
        metadata: { user_id: mockUserId },
        items: {
          data: [{ price: { id: mockPriceId } }],
        },
      }

      const mockPlan = {
        id: 'plan-id',
        credits_included: 80,
      }

      // Mock webhook event storage
      ;(supabaseAdmin.from as jest.Mock)
        .mockReturnValueOnce({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({ data: null, error: { code: 'PGRST116' } }),
            }),
          }),
        })
        .mockReturnValueOnce({
          insert: jest.fn().mockResolvedValue({ error: null }),
        })
        // Mock plan lookup
        .mockReturnValueOnce({
          select: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({ data: mockPlan, error: null }),
            }),
          }),
        })

      // Mock Stripe subscription retrieval
      const mockStripe = require('stripe')()
      mockStripe.subscriptions.retrieve.mockResolvedValue(mockSubscription)

      // Mock credits allocation
      jest.spyOn(StripeService, 'allocateCreditsToUser').mockResolvedValue()

      await StripeService.processWebhookEvent(mockEvent as any)

      expect(StripeService.allocateCreditsToUser).toHaveBeenCalledWith(
        mockUserId,
        mockPlan.credits_included,
        mockSubscription.id
      )
    })
  })
})

describe('CreditsManager', () => {
  const mockUserId = 'test-user-id'

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('hasEnoughCredits', () => {
    it('should return true if user has enough credits', async () => {
      const mockCredits = { current_credits: 50 }

      jest.spyOn(StripeService, 'getUserCredits').mockResolvedValue(mockCredits as any)

      const result = await CreditsManager.hasEnoughCredits(mockUserId, 'chat_message')

      expect(result.hasCredits).toBe(true)
      expect(result.currentCredits).toBe(50)
      expect(result.requiredCredits).toBe(1)
    })

    it('should return false if user does not have enough credits', async () => {
      const mockCredits = { current_credits: 0 }

      jest.spyOn(StripeService, 'getUserCredits').mockResolvedValue(mockCredits as any)

      const result = await CreditsManager.hasEnoughCredits(mockUserId, 'chat_message')

      expect(result.hasCredits).toBe(false)
      expect(result.currentCredits).toBe(0)
      expect(result.requiredCredits).toBe(1)
    })
  })

  describe('consumeCredits', () => {
    it('should consume credits successfully', async () => {
      jest.spyOn(CreditsManager, 'hasEnoughCredits').mockResolvedValue({
        hasCredits: true,
        currentCredits: 50,
        requiredCredits: 1,
      })

      jest.spyOn(StripeService, 'useCredits').mockResolvedValue({
        success: true,
        remainingCredits: 49,
      })

      const result = await CreditsManager.consumeCredits(mockUserId, 'chat_message', 'Test message')

      expect(result.success).toBe(true)
      expect(result.remainingCredits).toBe(49)
    })

    it('should fail if user does not have enough credits', async () => {
      jest.spyOn(CreditsManager, 'hasEnoughCredits').mockResolvedValue({
        hasCredits: false,
        currentCredits: 0,
        requiredCredits: 1,
      })

      const result = await CreditsManager.consumeCredits(mockUserId, 'chat_message')

      expect(result.success).toBe(false)
      expect(result.remainingCredits).toBe(0)
      expect(result.error).toContain('Insufficient credits')
    })
  })
})
