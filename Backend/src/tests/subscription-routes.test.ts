import request from 'supertest'
import express from 'express'
import subscriptionRoutes from '@/routes/subscriptions'
import webhookRoutes from '@/routes/webhook'
import { authenticateToken } from '@/middleware/auth'

// Mock dependencies
jest.mock('@/services/stripe')
jest.mock('@/middleware/auth')
jest.mock('@/lib')

const app = express()
app.use(express.json())
app.use('/api/subscriptions', subscriptionRoutes)
app.use('/api/webhook', webhookRoutes)

describe('Subscription Routes', () => {
  const mockUser = {
    sub: 'test-user-id',
    email: '<EMAIL>',
    role: 'user',
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock authentication middleware
    ;(authenticateToken as jest.Mock).mockImplementation((req: any, res: any, next: any) => {
      req.user = mockUser
      next()
    })
  })

  describe('GET /api/subscriptions/plans', () => {
    it('should return subscription plans', async () => {
      const mockPlans = [
        {
          id: '1',
          plan_name: 'starting',
          display_name: 'Starting Plan',
          price_amount: 999,
          credits_included: 80,
        },
        {
          id: '2',
          plan_name: 'scaling',
          display_name: 'Scaling Plan',
          price_amount: 1999,
          credits_included: 160,
        },
      ]

      const { StripeService } = require('@/services/stripe')
      StripeService.getSubscriptionPlans.mockResolvedValue(mockPlans)

      const response = await request(app)
        .get('/api/subscriptions/plans')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data).toEqual(mockPlans)
    })

    it('should handle errors when fetching plans', async () => {
      const { StripeService } = require('@/services/stripe')
      StripeService.getSubscriptionPlans.mockRejectedValue(new Error('Database error'))

      const response = await request(app)
        .get('/api/subscriptions/plans')
        .expect(500)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('Failed to fetch subscription plans')
    })
  })

  describe('POST /api/subscriptions/create-subscription', () => {
    it('should create subscription successfully', async () => {
      const mockProfile = { name: 'Test User', email: '<EMAIL>' }
      const mockPlan = {
        id: 'plan-id',
        display_name: 'Starting Plan',
        credits_included: 80,
        price_amount: 999,
      }
      const mockSubscription = { id: 'sub_test123' }
      const mockUserSubscription = { id: 'user-sub-id' }

      // Mock Supabase profile fetch
      const { supabaseAdmin } = require('@/lib')
      supabaseAdmin.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockProfile, error: null }),
          }),
        }),
      })

      const { StripeService } = require('@/services/stripe')
      StripeService.getUserSubscription.mockResolvedValue(null) // No existing subscription
      StripeService.getSubscriptionPlanByPriceId.mockResolvedValue(mockPlan)
      StripeService.createOrGetCustomer.mockResolvedValue('cus_test123')
      StripeService.createSubscription.mockResolvedValue({
        subscription: {
          ...mockSubscription,
          latest_invoice: {
            payment_intent: {
              client_secret: 'pi_test_client_secret',
            },
          },
        },
        userSubscription: mockUserSubscription,
      })

      const response = await request(app)
        .post('/api/subscriptions/create-subscription')
        .send({ priceId: 'price_test123' })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.subscription).toEqual(mockUserSubscription)
      expect(response.body.data.clientSecret).toBe('pi_test_client_secret')
    })

    it('should reject invalid price ID format', async () => {
      const response = await request(app)
        .post('/api/subscriptions/create-subscription')
        .send({ priceId: 'invalid-price-id' })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('Validation failed')
    })

    it('should reject if user already has subscription', async () => {
      const mockProfile = { name: 'Test User', email: '<EMAIL>' }
      const mockExistingSubscription = { id: 'existing-sub' }

      // Mock Supabase profile fetch
      const { supabaseAdmin } = require('@/lib')
      supabaseAdmin.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: mockProfile, error: null }),
          }),
        }),
      })

      const { StripeService } = require('@/services/stripe')
      StripeService.getUserSubscription.mockResolvedValue(mockExistingSubscription)

      const response = await request(app)
        .post('/api/subscriptions/create-subscription')
        .send({ priceId: 'price_test123' })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('User already has an active subscription')
    })
  })

  describe('GET /api/subscriptions/current', () => {
    it('should return current subscription and credits', async () => {
      const mockSubscription = { id: 'sub_test123', status: 'active' }
      const mockCredits = { current_credits: 50, total_credits_allocated: 80 }

      const { StripeService } = require('@/services/stripe')
      StripeService.getUserSubscription.mockResolvedValue(mockSubscription)
      StripeService.getUserCredits.mockResolvedValue(mockCredits)

      const response = await request(app)
        .get('/api/subscriptions/current')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.subscription).toEqual(mockSubscription)
      expect(response.body.data.credits).toEqual(mockCredits)
    })
  })

  describe('POST /api/subscriptions/cancel', () => {
    it('should cancel subscription at period end', async () => {
      const { StripeService } = require('@/services/stripe')
      StripeService.cancelSubscription.mockResolvedValue()

      const response = await request(app)
        .post('/api/subscriptions/cancel')
        .send({ cancelAtPeriodEnd: true })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toContain('canceled at the end of the current billing period')
      expect(StripeService.cancelSubscription).toHaveBeenCalledWith(mockUser.sub, true)
    })

    it('should cancel subscription immediately', async () => {
      const { StripeService } = require('@/services/stripe')
      StripeService.cancelSubscription.mockResolvedValue()

      const response = await request(app)
        .post('/api/subscriptions/cancel')
        .send({ cancelAtPeriodEnd: false })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.message).toContain('canceled immediately')
      expect(StripeService.cancelSubscription).toHaveBeenCalledWith(mockUser.sub, false)
    })
  })

  describe('POST /api/subscriptions/use-credits', () => {
    it('should use credits successfully', async () => {
      const { StripeService } = require('@/services/stripe')
      StripeService.useCredits.mockResolvedValue({
        success: true,
        remainingCredits: 47,
      })

      const response = await request(app)
        .post('/api/subscriptions/use-credits')
        .send({
          credits: 3,
          actionType: 'chat_message',
          description: 'Test action',
        })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.remainingCredits).toBe(47)
      expect(StripeService.useCredits).toHaveBeenCalledWith(
        mockUser.sub,
        3,
        'chat_message',
        'Test action'
      )
    })

    it('should reject invalid credits amount', async () => {
      const response = await request(app)
        .post('/api/subscriptions/use-credits')
        .send({
          credits: 0, // Invalid
          actionType: 'chat_message',
        })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('Validation failed')
    })

    it('should handle insufficient credits', async () => {
      const { StripeService } = require('@/services/stripe')
      StripeService.useCredits.mockResolvedValue({
        success: false,
        remainingCredits: 2,
      })

      const response = await request(app)
        .post('/api/subscriptions/use-credits')
        .send({
          credits: 5,
          actionType: 'chat_message',
        })
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('Insufficient credits')
      expect(response.body.remainingCredits).toBe(2)
    })
  })
})

describe('Webhook Routes', () => {
  describe('POST /api/webhook/stripe', () => {
    it('should process valid webhook', async () => {
      const mockEvent = {
        id: 'evt_test123',
        type: 'invoice.payment_succeeded',
        data: { object: {} },
      }

      const { StripeService } = require('@/services/stripe')
      StripeService.verifyWebhookSignature.mockReturnValue(mockEvent)
      StripeService.processWebhookEvent.mockResolvedValue()

      const response = await request(app)
        .post('/api/webhook/stripe')
        .set('stripe-signature', 'test-signature')
        .send(JSON.stringify({ test: 'data' }))
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.eventId).toBe(mockEvent.id)
      expect(response.body.eventType).toBe(mockEvent.type)
    })

    it('should reject webhook without signature', async () => {
      const response = await request(app)
        .post('/api/webhook/stripe')
        .send(JSON.stringify({ test: 'data' }))
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('Missing Stripe signature')
    })

    it('should handle invalid webhook signature', async () => {
      const { StripeService } = require('@/services/stripe')
      StripeService.verifyWebhookSignature.mockImplementation(() => {
        throw new Error('Invalid webhook signature')
      })

      const response = await request(app)
        .post('/api/webhook/stripe')
        .set('stripe-signature', 'invalid-signature')
        .send(JSON.stringify({ test: 'data' }))
        .expect(400)

      expect(response.body.success).toBe(false)
      expect(response.body.error).toBe('Invalid webhook signature')
    })
  })
})
